{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "set NODE_ENV=development && tsx server/index.ts", "dev:offline": "set NODE_ENV=development && set ENABLE_OFFLINE=true && tsx server/index.ts", "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist", "build:cpanel": "npm run build && npm run package:cpanel", "package:cpanel": "tar -czf santrimental-deploy.tar.gz --exclude=node_modules --exclude=.git --exclude=*.log dist package.json .env database data", "start": "set NODE_ENV=production && node dist/index.js", "start:offline": "set NODE_ENV=production && set ENABLE_OFFLINE=true && node dist/index.js", "check": "tsc", "db:push": "drizzle-kit push", "db:init": "node -e \"require('./server/sqlite-db.ts').initSQLiteTables()\"", "test:db": "node test-database.js", "test:db-config": "node test-database-config.js", "test:db-migration": "node test-database-migration.js", "test:db-performance": "node test-database-performance.js", "test:db-all": "node run-all-database-tests.js", "test:db-quick": "npm run test:db-config && npm run test:db", "test:endpoints": "node test-endpoints.js", "test:server": "node test-server-simple.js", "db:status": "node database-status.js", "db:test-suite": "node run-database-tests.js"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.10.0", "@jridgewell/trace-mapping": "^0.3.25", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@supabase/supabase-js": "^2.54.0", "@tanstack/react-query": "^5.60.5", "@types/bcrypt": "^6.0.0", "@types/better-sqlite3": "^7.6.13", "@types/jsonwebtoken": "^9.0.10", "@types/nodemailer": "^6.4.17", "@types/passport-google-oauth20": "^2.0.16", "axios": "^1.11.0", "bcrypt": "^6.0.0", "better-sqlite3": "^12.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "connect-pg-simple": "^10.0.0", "date-fns": "^3.6.0", "dexie": "^4.0.11", "dotenv": "^17.2.1", "drizzle-orm": "^0.39.1", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.6.0", "express": "^4.21.2", "express-session": "^1.18.1", "framer-motion": "^11.13.1", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.453.0", "memorystore": "^1.6.7", "mysql2": "^3.14.3", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "nodemailer": "^7.0.5", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-local": "^1.0.0", "postgres": "^3.4.7", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-icons": "^5.4.0", "react-resizable-panels": "^2.1.7", "react-router-dom": "^7.8.0", "recharts": "^2.15.2", "sonner": "^2.0.7", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "vaul": "^1.1.2", "workbox-webpack-plugin": "^7.3.0", "wouter": "^3.3.5", "ws": "^8.18.0", "zod": "^3.24.2", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@replit/vite-plugin-cartographer": "^0.2.5", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.1.3", "@types/connect-pg-simple": "^7.0.3", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/node": "20.16.11", "@types/passport": "^1.0.17", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/ws": "^8.5.13", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "drizzle-kit": "^0.30.4", "esbuild": "^0.25.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.17", "tsx": "^4.19.1", "typescript": "5.6.3", "vite": "^5.4.14"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}