#!/usr/bin/env node

/**
 * 🔄 Database Migration Testing Script
 * 
 * Tests database migration and data synchronization:
 * - Schema migration testing
 * - Data migration between providers
 * - Backup and restore functionality
 * - Data integrity validation
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:5000';
const API_BASE = `${BASE_URL}/api`;

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  console.log('\n' + '='.repeat(60));
  log(`🔄 ${message}`, 'cyan');
  console.log('='.repeat(60));
}

function logSubHeader(message) {
  console.log('\n' + '-'.repeat(40));
  log(`📋 ${message}`, 'yellow');
  console.log('-'.repeat(40));
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Test results storage
const migrationResults = {
  schema: {},
  migration: {},
  backup: {},
  integrity: {},
  summary: { total: 0, passed: 0, failed: 0, warnings: 0 }
};

function updateResult(category, test, status, details = '') {
  if (!migrationResults[category]) {
    migrationResults[category] = {};
  }
  
  migrationResults[category][test] = {
    status,
    details,
    timestamp: new Date().toISOString()
  };
  
  migrationResults.summary.total++;
  if (status === 'passed') migrationResults.summary.passed++;
  else if (status === 'failed') migrationResults.summary.failed++;
  else if (status === 'warning') migrationResults.summary.warnings++;
}

// Test schema files
function testSchemaFiles() {
  logSubHeader('Database Schema Files Check');
  
  const schemaFiles = [
    { path: 'database/mysql-schema.sql', description: 'MySQL schema' },
    { path: 'database/schema.sql', description: 'Generic schema' },
    { path: 'database/schema_neondb.sql', description: 'NEON PostgreSQL schema' }
  ];
  
  schemaFiles.forEach(({ path: filePath, description }) => {
    const fullPath = path.join(process.cwd(), filePath);
    
    if (fs.existsSync(fullPath)) {
      try {
        const content = fs.readFileSync(fullPath, 'utf8');
        const lines = content.split('\n').length;
        const size = content.length;
        
        logSuccess(`${description}: Found ✅`);
        logInfo(`  Path: ${filePath}`);
        logInfo(`  Size: ${size} bytes`);
        logInfo(`  Lines: ${lines}`);
        
        // Check for common SQL keywords
        const hasCreateTable = content.toLowerCase().includes('create table');
        const hasIndexes = content.toLowerCase().includes('create index');
        const hasConstraints = content.toLowerCase().includes('constraint');
        
        logInfo(`  Contains CREATE TABLE: ${hasCreateTable ? '✅' : '❌'}`);
        logInfo(`  Contains INDEXES: ${hasIndexes ? '✅' : '⚠️'}`);
        logInfo(`  Contains CONSTRAINTS: ${hasConstraints ? '✅' : '⚠️'}`);
        
        updateResult('schema', filePath.replace('/', '-'), 'passed', `${lines} lines, ${size} bytes`);
        
      } catch (error) {
        logError(`${description}: Read error ❌`);
        logError(`  Error: ${error.message}`);
        updateResult('schema', filePath.replace('/', '-'), 'failed', error.message);
      }
    } else {
      logWarning(`${description}: Not found ⚠️`);
      logInfo(`  Expected path: ${filePath}`);
      updateResult('schema', filePath.replace('/', '-'), 'warning', 'File not found');
    }
  });
}

// Test migration API endpoints
async function testMigrationAPIs() {
  logSubHeader('Migration API Endpoints');
  
  const endpoints = [
    { path: '/database/migrate/status', method: 'GET', description: 'Migration status' },
    { path: '/database/migrate/list', method: 'GET', description: 'Available migrations' },
    { path: '/database/backup/create', method: 'POST', description: 'Create backup' },
    { path: '/database/backup/list', method: 'GET', description: 'List backups' }
  ];
  
  for (const endpoint of endpoints) {
    try {
      logInfo(`Testing ${endpoint.description}...`);
      
      let response;
      if (endpoint.method === 'GET') {
        response = await axios.get(`${API_BASE}${endpoint.path}`, { timeout: 10000 });
      } else {
        response = await axios.post(`${API_BASE}${endpoint.path}`, {}, { timeout: 10000 });
      }
      
      if (response.status === 200) {
        logSuccess(`${endpoint.description}: Available ✅`);
        logInfo(`  Response: ${JSON.stringify(response.data, null, 2)}`);
        updateResult('migration', endpoint.path.replace('/', '-'), 'passed', 'Endpoint available');
      } else {
        logWarning(`${endpoint.description}: Unexpected status ${response.status} ⚠️`);
        updateResult('migration', endpoint.path.replace('/', '-'), 'warning', `Status: ${response.status}`);
      }
      
    } catch (error) {
      if (error.response && error.response.status === 404) {
        logWarning(`${endpoint.description}: Not implemented ⚠️`);
        updateResult('migration', endpoint.path.replace('/', '-'), 'warning', 'Endpoint not implemented');
      } else {
        logError(`${endpoint.description}: Error ❌`);
        logError(`  Error: ${error.message}`);
        updateResult('migration', endpoint.path.replace('/', '-'), 'failed', error.message);
      }
    }
  }
}

// Test data backup functionality
async function testBackupFunctionality() {
  logSubHeader('Data Backup Testing');
  
  try {
    // Create test data first
    logInfo('Creating test data for backup...');
    
    const testUser = {
      email: `backup-test-${Date.now()}@example.com`,
      password: 'testpassword123'
    };
    
    const registerResponse = await axios.post(`${API_BASE}/auth/signup`, testUser);
    
    if (registerResponse.data.success) {
      logSuccess('Test user created for backup testing ✅');
      
      // Try to create backup
      try {
        const backupResponse = await axios.post(`${API_BASE}/database/backup/create`, {
          name: `test-backup-${Date.now()}`,
          description: 'Automated test backup'
        });
        
        if (backupResponse.data.success) {
          logSuccess('Backup creation successful ✅');
          logInfo(`  Backup info: ${JSON.stringify(backupResponse.data, null, 2)}`);
          updateResult('backup', 'create', 'passed', 'Backup created successfully');
        } else {
          logWarning('Backup creation returned success=false ⚠️');
          updateResult('backup', 'create', 'warning', 'Backup API returned false');
        }
        
      } catch (backupError) {
        if (backupError.response && backupError.response.status === 404) {
          logWarning('Backup API not implemented ⚠️');
          updateResult('backup', 'create', 'warning', 'Backup API not implemented');
        } else {
          logError(`Backup creation failed: ${backupError.message}`);
          updateResult('backup', 'create', 'failed', backupError.message);
        }
      }
      
    } else {
      logWarning('Could not create test user for backup testing ⚠️');
      updateResult('backup', 'test-data', 'warning', 'Test user creation failed');
    }
    
  } catch (error) {
    logError(`Backup testing failed: ${error.message}`);
    updateResult('backup', 'functionality', 'failed', error.message);
  }
}

// Test data integrity
async function testDataIntegrity() {
  logSubHeader('Data Integrity Testing');
  
  try {
    // Get current database status
    const statusResponse = await axios.get(`${API_BASE}/database/status`);
    const currentProvider = statusResponse.data.data?.currentProvider;
    
    if (!currentProvider) {
      logError('Cannot determine current database provider ❌');
      updateResult('integrity', 'provider-detection', 'failed', 'Provider not detected');
      return;
    }
    
    logInfo(`Current provider: ${currentProvider}`);
    
    // Create test data
    const testData = {
      email: `integrity-test-${Date.now()}@example.com`,
      password: 'testpassword123'
    };
    
    logInfo('Creating test data...');
    const createResponse = await axios.post(`${API_BASE}/auth/signup`, testData);
    
    if (!createResponse.data.success) {
      logError('Failed to create test data ❌');
      updateResult('integrity', 'test-data-creation', 'failed', 'Could not create test data');
      return;
    }
    
    logSuccess('Test data created ✅');
    
    // Verify data can be read back
    logInfo('Verifying data integrity...');
    const loginResponse = await axios.post(`${API_BASE}/auth/signin`, testData);
    
    if (loginResponse.data.success) {
      logSuccess('Data integrity verified ✅');
      logInfo('  Data can be written and read back correctly');
      updateResult('integrity', 'read-write', 'passed', 'Data integrity verified');
    } else {
      logError('Data integrity check failed ❌');
      logError('  Data was created but cannot be read back');
      updateResult('integrity', 'read-write', 'failed', 'Data read-back failed');
    }
    
    // Test concurrent operations
    logInfo('Testing concurrent operations...');
    
    const concurrentPromises = [];
    for (let i = 0; i < 5; i++) {
      const concurrentData = {
        email: `concurrent-${i}-${Date.now()}@example.com`,
        password: 'testpassword123'
      };
      concurrentPromises.push(axios.post(`${API_BASE}/auth/signup`, concurrentData));
    }
    
    try {
      const results = await Promise.all(concurrentPromises);
      const successCount = results.filter(r => r.data.success).length;
      
      logSuccess(`Concurrent operations: ${successCount}/5 successful ✅`);
      updateResult('integrity', 'concurrent-ops', 'passed', `${successCount}/5 operations successful`);
      
    } catch (concurrentError) {
      logWarning(`Concurrent operations had issues: ${concurrentError.message} ⚠️`);
      updateResult('integrity', 'concurrent-ops', 'warning', concurrentError.message);
    }
    
  } catch (error) {
    logError(`Data integrity testing failed: ${error.message}`);
    updateResult('integrity', 'testing', 'failed', error.message);
  }
}

// Test provider switching with data preservation
async function testProviderSwitchingWithData() {
  logSubHeader('Provider Switching with Data Preservation');
  
  try {
    // Get current provider
    const statusResponse = await axios.get(`${API_BASE}/database/status`);
    const originalProvider = statusResponse.data.data?.currentProvider;
    
    if (!originalProvider) {
      logError('Cannot determine current provider ❌');
      updateResult('migration', 'provider-switching', 'failed', 'Provider not detected');
      return;
    }
    
    logInfo(`Original provider: ${originalProvider}`);
    
    // Create test data in original provider
    const testData = {
      email: `switch-test-${Date.now()}@example.com`,
      password: 'testpassword123'
    };
    
    logInfo('Creating test data in original provider...');
    const createResponse = await axios.post(`${API_BASE}/auth/signup`, testData);
    
    if (!createResponse.data.success) {
      logWarning('Could not create test data for switching test ⚠️');
      updateResult('migration', 'test-data-creation', 'warning', 'Test data creation failed');
      return;
    }
    
    logSuccess('Test data created in original provider ✅');
    
    // Try switching to different providers
    const providers = ['sqlite', 'mysql', 'aiven-mysql', 'neon-postgresql'];
    const alternativeProviders = providers.filter(p => p !== originalProvider);
    
    for (const targetProvider of alternativeProviders) {
      try {
        logInfo(`Attempting to switch to ${targetProvider}...`);
        
        const switchResponse = await axios.post(`${API_BASE}/database/switch`, {
          provider: targetProvider
        });
        
        if (switchResponse.data.success) {
          logSuccess(`Successfully switched to ${targetProvider} ✅`);
          
          // Wait for connection to stabilize
          await new Promise(resolve => setTimeout(resolve, 2000));
          
          // Test if we can still access data (this would require migration)
          try {
            const loginResponse = await axios.post(`${API_BASE}/auth/signin`, testData);
            
            if (loginResponse.data.success) {
              logSuccess(`Data accessible in ${targetProvider} ✅`);
              updateResult('migration', `switch-to-${targetProvider}`, 'passed', 'Data preserved after switch');
            } else {
              logWarning(`Data not accessible in ${targetProvider} ⚠️`);
              logInfo('  This is expected if migration is not implemented');
              updateResult('migration', `switch-to-${targetProvider}`, 'warning', 'Data not migrated');
            }
          } catch (dataError) {
            logWarning(`Data access error in ${targetProvider}: ${dataError.message} ⚠️`);
            updateResult('migration', `switch-to-${targetProvider}`, 'warning', 'Data access error');
          }
          
          // Switch back to original provider
          try {
            await axios.post(`${API_BASE}/database/switch`, {
              provider: originalProvider
            });
            logInfo(`Switched back to ${originalProvider}`);
          } catch (switchBackError) {
            logWarning(`Could not switch back to ${originalProvider}: ${switchBackError.message}`);
          }
          
        } else {
          logWarning(`Switch to ${targetProvider} failed: ${switchResponse.data.error} ⚠️`);
          updateResult('migration', `switch-to-${targetProvider}`, 'warning', switchResponse.data.error);
        }
        
      } catch (switchError) {
        logWarning(`Switch to ${targetProvider} error: ${switchError.message} ⚠️`);
        updateResult('migration', `switch-to-${targetProvider}`, 'warning', switchError.message);
      }
    }
    
  } catch (error) {
    logError(`Provider switching test failed: ${error.message}`);
    updateResult('migration', 'provider-switching', 'failed', error.message);
  }
}

// Generate migration report
function generateMigrationReport() {
  logHeader('Migration Test Report');
  
  const { total, passed, failed, warnings } = migrationResults.summary;
  
  logInfo(`Total Tests: ${total}`);
  logSuccess(`Passed: ${passed}`);
  logError(`Failed: ${failed}`);
  logWarning(`Warnings: ${warnings}`);
  
  const successRate = total > 0 ? ((passed / total) * 100).toFixed(1) : 0;
  logInfo(`Success Rate: ${successRate}%`);
  
  // Save detailed report
  const reportPath = path.join(__dirname, 'database-migration-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(migrationResults, null, 2));
  logInfo(`Detailed report saved to: ${reportPath}`);
  
  // Migration readiness assessment
  logSubHeader('Migration Readiness Assessment');
  
  if (failed === 0) {
    if (warnings === 0) {
      logSuccess('🎉 Migration system is fully ready!');
    } else {
      logWarning('⚠️  Migration system is functional but some features are not implemented.');
    }
  } else {
    logError('❌ Migration system has critical issues that need to be addressed.');
  }
}

// Main test runner
async function runMigrationTests() {
  logHeader('Database Migration Testing Suite');
  
  testSchemaFiles();
  await testMigrationAPIs();
  await testBackupFunctionality();
  await testDataIntegrity();
  await testProviderSwitchingWithData();
  generateMigrationReport();
}

// Run tests if this file is executed directly
if (require.main === module) {
  runMigrationTests().catch(error => {
    logError(`Migration test suite failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  runMigrationTests,
  migrationResults
};
