#!/usr/bin/env node

/**
 * 🔧 Database Configuration Testing Script
 * 
 * Tests database configuration and environment setup:
 * - Environment variables validation
 * - Database URLs validation
 * - SSL certificate validation
 * - Connection string parsing
 */

const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  console.log('\n' + '='.repeat(60));
  log(`🔧 ${message}`, 'cyan');
  console.log('='.repeat(60));
}

function logSubHeader(message) {
  console.log('\n' + '-'.repeat(40));
  log(`📋 ${message}`, 'yellow');
  console.log('-'.repeat(40));
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Load environment variables
require('dotenv').config();

// Test results
const configResults = {
  environment: {},
  connections: {},
  certificates: {},
  summary: { total: 0, passed: 0, failed: 0, warnings: 0 }
};

function updateResult(category, test, status, details = '') {
  if (!configResults[category]) {
    configResults[category] = {};
  }
  
  configResults[category][test] = { status, details, timestamp: new Date().toISOString() };
  configResults.summary.total++;
  
  if (status === 'passed') configResults.summary.passed++;
  else if (status === 'failed') configResults.summary.failed++;
  else if (status === 'warning') configResults.summary.warnings++;
}

// Test environment variables
function testEnvironmentVariables() {
  logSubHeader('Environment Variables Check');
  
  const requiredVars = [
    { name: 'DATABASE_URL', required: false, description: 'Local MySQL connection' },
    { name: 'AIVEN_MYSQL_URL', required: false, description: 'AIVEN MySQL connection' },
    { name: 'NEON_DATABASE_URL', required: false, description: 'NEON PostgreSQL connection' },
    { name: 'DATABASE_PROVIDER', required: false, description: 'Database provider selection' },
    { name: 'JWT_SECRET', required: true, description: 'JWT secret key' },
    { name: 'SMTP_HOST', required: false, description: 'Email SMTP host' },
    { name: 'SMTP_USER', required: false, description: 'Email SMTP user' },
    { name: 'SMTP_PASS', required: false, description: 'Email SMTP password' }
  ];
  
  let hasAtLeastOneDB = false;
  
  requiredVars.forEach(({ name, required, description }) => {
    const value = process.env[name];
    
    if (value) {
      logSuccess(`${name}: Set ✅`);
      logInfo(`  Description: ${description}`);
      
      // Mask sensitive values
      if (name.includes('PASS') || name.includes('SECRET')) {
        logInfo(`  Value: ${'*'.repeat(value.length)}`);
      } else if (name.includes('URL')) {
        // Show URL but mask password
        const maskedUrl = value.replace(/:([^:@]+)@/, ':****@');
        logInfo(`  Value: ${maskedUrl}`);
      } else {
        logInfo(`  Value: ${value}`);
      }
      
      updateResult('environment', name, 'passed', 'Variable is set');
      
      // Check if it's a database URL
      if (name.includes('DATABASE') || name.includes('URL')) {
        hasAtLeastOneDB = true;
      }
      
    } else {
      if (required) {
        logError(`${name}: Missing ❌ (Required)`);
        updateResult('environment', name, 'failed', 'Required variable missing');
      } else {
        logWarning(`${name}: Not set ⚠️  (Optional)`);
        updateResult('environment', name, 'warning', 'Optional variable not set');
      }
      logInfo(`  Description: ${description}`);
    }
  });
  
  // Check if at least one database is configured
  if (hasAtLeastOneDB) {
    logSuccess('At least one database is configured ✅');
    updateResult('environment', 'database-configured', 'passed', 'Database configuration found');
  } else {
    logError('No database configured ❌');
    updateResult('environment', 'database-configured', 'failed', 'No database URLs found');
  }
}

// Test database URL parsing
function testDatabaseURLs() {
  logSubHeader('Database URL Validation');
  
  const dbConfigs = [
    { name: 'Local MySQL', url: process.env.DATABASE_URL },
    { name: 'AIVEN MySQL', url: process.env.AIVEN_MYSQL_URL },
    { name: 'NEON PostgreSQL', url: process.env.NEON_DATABASE_URL }
  ];
  
  dbConfigs.forEach(({ name, url }) => {
    if (!url) {
      logWarning(`${name}: URL not configured ⚠️`);
      updateResult('connections', name.toLowerCase().replace(' ', '-'), 'warning', 'URL not configured');
      return;
    }
    
    try {
      const parsedUrl = new URL(url);
      
      logSuccess(`${name}: URL format valid ✅`);
      logInfo(`  Protocol: ${parsedUrl.protocol}`);
      logInfo(`  Host: ${parsedUrl.hostname}`);
      logInfo(`  Port: ${parsedUrl.port || 'default'}`);
      logInfo(`  Database: ${parsedUrl.pathname.substring(1)}`);
      logInfo(`  Username: ${parsedUrl.username || 'not specified'}`);
      logInfo(`  Password: ${parsedUrl.password ? '****' : 'not specified'}`);
      
      // Check SSL parameters
      const searchParams = parsedUrl.searchParams;
      if (searchParams.has('ssl-mode') || searchParams.has('sslmode')) {
        logInfo(`  SSL Mode: ${searchParams.get('ssl-mode') || searchParams.get('sslmode')}`);
      }
      
      updateResult('connections', name.toLowerCase().replace(' ', '-'), 'passed', 'URL format valid');
      
    } catch (error) {
      logError(`${name}: Invalid URL format ❌`);
      logError(`  Error: ${error.message}`);
      updateResult('connections', name.toLowerCase().replace(' ', '-'), 'failed', `Invalid URL: ${error.message}`);
    }
  });
}

// Test SSL certificates
function testSSLCertificates() {
  logSubHeader('SSL Certificates Check');
  
  // Check AIVEN CA certificate
  const aivenCertPath = process.env.AIVEN_CA_CERT_PATH;
  const aivenCertContent = process.env.AIVEN_CA_CERT;
  
  if (aivenCertPath) {
    logInfo(`AIVEN CA Certificate Path: ${aivenCertPath}`);
    
    if (fs.existsSync(aivenCertPath)) {
      try {
        const certContent = fs.readFileSync(aivenCertPath, 'utf8');
        
        if (certContent.includes('BEGIN CERTIFICATE') && certContent.includes('END CERTIFICATE')) {
          logSuccess('AIVEN CA Certificate file is valid ✅');
          logInfo(`  File size: ${certContent.length} bytes`);
          updateResult('certificates', 'aiven-file', 'passed', 'Certificate file valid');
        } else {
          logError('AIVEN CA Certificate file format invalid ❌');
          updateResult('certificates', 'aiven-file', 'failed', 'Invalid certificate format');
        }
        
      } catch (error) {
        logError(`Failed to read AIVEN CA Certificate: ${error.message}`);
        updateResult('certificates', 'aiven-file', 'failed', `Read error: ${error.message}`);
      }
    } else {
      logError(`AIVEN CA Certificate file not found: ${aivenCertPath}`);
      updateResult('certificates', 'aiven-file', 'failed', 'Certificate file not found');
    }
  }
  
  if (aivenCertContent) {
    logInfo('AIVEN CA Certificate (environment variable)');
    
    if (aivenCertContent.includes('BEGIN CERTIFICATE') && aivenCertContent.includes('END CERTIFICATE')) {
      logSuccess('AIVEN CA Certificate content is valid ✅');
      logInfo(`  Content length: ${aivenCertContent.length} bytes`);
      updateResult('certificates', 'aiven-env', 'passed', 'Certificate content valid');
    } else {
      logError('AIVEN CA Certificate content format invalid ❌');
      updateResult('certificates', 'aiven-env', 'failed', 'Invalid certificate format');
    }
  }
  
  if (!aivenCertPath && !aivenCertContent) {
    logWarning('No AIVEN CA Certificate configured ⚠️');
    logInfo('  This is required for AIVEN MySQL SSL connections');
    updateResult('certificates', 'aiven', 'warning', 'No certificate configured');
  }
}

// Test actual database connections
async function testDatabaseConnections() {
  logSubHeader('Database Connection Testing');
  
  // Test Local MySQL
  if (process.env.DATABASE_URL) {
    await testMySQLConnection('Local MySQL', process.env.DATABASE_URL, false);
  }
  
  // Test AIVEN MySQL
  if (process.env.AIVEN_MYSQL_URL) {
    const sslConfig = {};
    
    if (process.env.AIVEN_CA_CERT_PATH && fs.existsSync(process.env.AIVEN_CA_CERT_PATH)) {
      sslConfig.ca = fs.readFileSync(process.env.AIVEN_CA_CERT_PATH);
    } else if (process.env.AIVEN_CA_CERT) {
      sslConfig.ca = process.env.AIVEN_CA_CERT;
    }
    
    if (Object.keys(sslConfig).length > 0) {
      sslConfig.rejectUnauthorized = true;
    }
    
    await testMySQLConnection('AIVEN MySQL', process.env.AIVEN_MYSQL_URL, sslConfig);
  }
  
  // Test SQLite (always available)
  await testSQLiteConnection();
}

async function testMySQLConnection(name, url, sslConfig) {
  logInfo(`Testing ${name} connection...`);
  
  try {
    const connection = await mysql.createConnection({
      uri: url,
      ssl: sslConfig,
      connectTimeout: 10000
    });
    
    // Test basic query
    const [rows] = await connection.execute('SELECT 1 as test');
    await connection.end();
    
    logSuccess(`${name}: Connection successful ✅`);
    logInfo(`  Test query result: ${JSON.stringify(rows[0])}`);
    updateResult('connections', name.toLowerCase().replace(' ', '-') + '-test', 'passed', 'Connection successful');
    
  } catch (error) {
    logError(`${name}: Connection failed ❌`);
    logError(`  Error: ${error.message}`);
    updateResult('connections', name.toLowerCase().replace(' ', '-') + '-test', 'failed', error.message);
  }
}

async function testSQLiteConnection() {
  logInfo('Testing SQLite connection...');
  
  try {
    const Database = require('better-sqlite3');
    const dbPath = path.join(process.cwd(), 'data', 'santrimental.db');
    
    // Ensure data directory exists
    const dataDir = path.dirname(dbPath);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
      logInfo('  Created data directory');
    }
    
    const db = new Database(dbPath);
    const result = db.prepare('SELECT 1 as test').get();
    db.close();
    
    logSuccess('SQLite: Connection successful ✅');
    logInfo(`  Database path: ${dbPath}`);
    logInfo(`  Test query result: ${JSON.stringify(result)}`);
    updateResult('connections', 'sqlite-test', 'passed', 'Connection successful');
    
  } catch (error) {
    logError(`SQLite: Connection failed ❌`);
    logError(`  Error: ${error.message}`);
    updateResult('connections', 'sqlite-test', 'failed', error.message);
  }
}

// Generate configuration report
function generateConfigReport() {
  logHeader('Configuration Test Report');
  
  const { total, passed, failed, warnings } = configResults.summary;
  
  logInfo(`Total Tests: ${total}`);
  logSuccess(`Passed: ${passed}`);
  logError(`Failed: ${failed}`);
  logWarning(`Warnings: ${warnings}`);
  
  const successRate = total > 0 ? ((passed / total) * 100).toFixed(1) : 0;
  logInfo(`Success Rate: ${successRate}%`);
  
  // Save detailed report
  const reportPath = path.join(__dirname, 'database-config-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(configResults, null, 2));
  logInfo(`Detailed report saved to: ${reportPath}`);
  
  // Recommendations
  logSubHeader('Recommendations');
  
  if (failed > 0) {
    logError('❌ Critical issues found. Please fix the failed tests before proceeding.');
  }
  
  if (warnings > 0) {
    logWarning('⚠️  Some optional configurations are missing. Consider setting them up for full functionality.');
  }
  
  if (failed === 0 && warnings === 0) {
    logSuccess('🎉 All configurations are properly set up!');
  }
}

// Main test runner
async function runConfigTests() {
  logHeader('Database Configuration Testing Suite');
  
  testEnvironmentVariables();
  testDatabaseURLs();
  testSSLCertificates();
  await testDatabaseConnections();
  generateConfigReport();
}

// Run tests if this file is executed directly
if (require.main === module) {
  runConfigTests().catch(error => {
    logError(`Configuration test suite failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  runConfigTests,
  configResults
};
