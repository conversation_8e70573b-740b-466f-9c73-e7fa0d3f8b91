import mysql from 'mysql2/promise';
import { drizzle } from 'drizzle-orm/mysql2';
import * as schema from "@shared/schema";

export type DatabaseProvider = 'mysql' | 'aiven-mysql' | 'postgresql' | 'neon-postgresql' | 'sqlite';

export interface DatabaseConfig {
  provider: DatabaseProvider;
  url: string;
  ssl?: boolean | object;
  poolConfig?: {
    connectionLimit?: number;
    queueLimit?: number;
    acquireTimeout?: number;
    timeout?: number;
  };
  caCertPath?: string;
}

export function getDatabaseConfig(): DatabaseConfig {
  console.log('🔍 Detecting database configuration...');

  // AIVEN MySQL Detection (Highest Priority)
  if (process.env.AIVEN_MYSQL_URL || process.env.DATABASE_PROVIDER === 'aiven-mysql') {
    console.log('🎯 AIVEN MySQL detected');
    const config: DatabaseConfig = {
      provider: 'aiven-mysql',
      url: process.env.AIVEN_MYSQL_URL || process.env.DATABASE_URL!,
      ssl: {
        rejectUnauthorized: true,
        ca: process.env.AIVEN_CA_CERT
      },
      poolConfig: {
        connectionLimit: 20,
        queueLimit: 0,
        acquireTimeout: 60000,
        timeout: 60000
      }
    };

    // Support certificate file path
    if (process.env.AIVEN_CA_CERT_PATH) {
      config.caCertPath = process.env.AIVEN_CA_CERT_PATH;
    }

    return config;
  }

  // NEON PostgreSQL Detection
  if (process.env.NEON_DATABASE_URL || process.env.DATABASE_PROVIDER === 'neon-postgresql') {
    return {
      provider: 'neon-postgresql',
      url: process.env.NEON_DATABASE_URL || process.env.DATABASE_URL!,
      ssl: true,
      poolConfig: { connectionLimit: 20 }
    };
  }

  // Local MySQL
  if (process.env.DATABASE_URL?.includes('mysql')) {
    return {
      provider: 'mysql',
      url: process.env.DATABASE_URL,
      ssl: false,
      poolConfig: { connectionLimit: 10 }
    };
  }

  // Fallback to SQLite
  return {
    provider: 'sqlite',
    url: './data/santrimental.db',
    ssl: false
  };
}