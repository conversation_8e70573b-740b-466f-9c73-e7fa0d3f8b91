import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClientProvider } from "@tanstack/react-query";
import { Router, Route, Switch } from "wouter";
import { queryClient } from "@/lib/queryClient";
import Index from "./pages/Index";
import Login from "./pages/Login";
import SimpleLogin from "./pages/SimpleLogin";
import Register from "./pages/Register";
import Education from "./pages/Education";
import Movies from "./pages/MoviesNew";
import Downloads from "./pages/DownloadsNew";
import Games from "./pages/GamesNew";
import DatabaseDashboard from "./pages/DatabaseDashboard";
import Test from "./pages/Test";
import NotFound from "./pages/NotFound";
import AssessmentsPage from "./pages/Assessments";

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <Router>
        <Switch>
          <Route path="/" component={Index} />
          <Route path="/test" component={Test} />
          <Route path="/login" component={SimpleLogin} />
          <Route path="/login-full" component={Login} />
          <Route path="/register" component={Register} />
          <Route path="/education" component={Education} />
          <Route path="/movies" component={Movies} />
          <Route path="/downloads" component={Downloads} />
          <Route path="/games" component={Games} />
          <Route path="/database" component={DatabaseDashboard} />
          <Route path="/assessments" component={AssessmentsPage} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route component={NotFound} />
        </Switch>
      </Router>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
