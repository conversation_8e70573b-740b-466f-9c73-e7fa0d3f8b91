/**
 * 🔧 Admin Dashboard Routes
 * 
 * Comprehensive admin management system
 */

import { Router } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { ConnectionManager } from '../database/connection-manager.js';
import { 
  authenticateToken, 
  requireAdmin, 
  requirePermission,
  hashPassword,
  AuthenticatedRequest 
} from '../middleware/auth.js';

const router = Router();

// Apply authentication and admin role requirement to all routes
router.use(authenticateToken);
router.use(requireAdmin);

/**
 * Admin Dashboard Overview
 */
router.get('/dashboard', requirePermission('admin.dashboard'), async (req: AuthenticatedRequest, res) => {
  try {
    const connectionManager = ConnectionManager.getInstance();
    const connection = await connectionManager.getConnection();
    const provider = connectionManager.getCurrentProvider();

    let stats;
    if (provider === 'sqlite') {
      const totalUsers = connection.db.prepare('SELECT COUNT(*) as count FROM users').get();
      const activeUsers = connection.db.prepare('SELECT COUNT(*) as count FROM users WHERE is_active = 1').get();
      const totalAssessments = connection.db.prepare('SELECT COUNT(*) as count FROM assessment_sessions').get();
      const completedAssessments = connection.db.prepare('SELECT COUNT(*) as count FROM assessment_sessions WHERE session_status = "completed"').get();
      
      stats = {
        totalUsers: totalUsers.count,
        activeUsers: activeUsers.count,
        totalAssessments: totalAssessments.count,
        completedAssessments: completedAssessments.count
      };
    } else {
      const queries = [
        'SELECT COUNT(*) as count FROM users',
        'SELECT COUNT(*) as count FROM users WHERE status = "active"',
        // Use placeholder values for assessments since tables don't exist yet
        'SELECT 0 as count',
        'SELECT 0 as count'
      ];
      
      const results = await Promise.all(
        queries.map(query => connection.getPool().execute(query))
      );
      
      stats = {
        totalUsers: (results[0] as any)[0][0].count,
        activeUsers: (results[1] as any)[0][0].count,
        totalAssessments: (results[2] as any)[0][0].count,
        completedAssessments: (results[3] as any)[0][0].count
      };
    }

    // Recent activity
    let recentUsers;
    if (provider === 'sqlite') {
      const stmt = connection.db.prepare(`
        SELECT u.id, u.email, u.role, u.created_at, p.nama_lengkap
        FROM users u
        LEFT JOIN profiles p ON u.id = p.user_id
        ORDER BY u.created_at DESC
        LIMIT 5
      `);
      recentUsers = stmt.all();
    } else {
      const result = await connection.getPool().execute(`
        SELECT id, username, email, role, created_at, first_name as nama_lengkap
        FROM users
        ORDER BY created_at DESC
        LIMIT 5
      `);
      recentUsers = (result as any)[0];
    }

    res.json({
      success: true,
      data: {
        stats,
        recentUsers,
        systemInfo: {
          currentProvider: provider,
          serverTime: new Date().toISOString(),
          version: '1.0.0'
        }
      }
    });

  } catch (error) {
    console.error('Dashboard error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to load dashboard data'
    });
  }
});

/**
 * User Management - List Users
 */
router.get('/users', requirePermission('admin.users.view'), async (req: AuthenticatedRequest, res) => {
  try {
    const { page = 1, limit = 10, search = '', role = '' } = req.query;
    const offset = (Number(page) - 1) * Number(limit);

    const connectionManager = ConnectionManager.getInstance();
    const connection = await connectionManager.getConnection();
    const provider = connectionManager.getCurrentProvider();

    let users, totalCount;
    
    if (provider === 'sqlite') {
      let whereClause = 'WHERE 1=1';
      const params: any[] = [];
      
      if (search) {
        whereClause += ' AND (u.email LIKE ? OR p.nama_lengkap LIKE ?)';
        params.push(`%${search}%`, `%${search}%`);
      }
      
      if (role) {
        whereClause += ' AND u.role = ?';
        params.push(role);
      }

      const countStmt = connection.db.prepare(`
        SELECT COUNT(*) as count 
        FROM users u 
        LEFT JOIN profiles p ON u.id = p.user_id 
        ${whereClause}
      `);
      totalCount = countStmt.get(...params).count;

      const usersStmt = connection.db.prepare(`
        SELECT u.id, u.email, u.role, u.is_active, u.email_verified, 
               u.last_login_at, u.created_at, p.nama_lengkap
        FROM users u
        LEFT JOIN profiles p ON u.id = p.user_id
        ${whereClause}
        ORDER BY u.created_at DESC
        LIMIT ? OFFSET ?
      `);
      users = usersStmt.all(...params, Number(limit), offset);
    } else {
      let whereClause = 'WHERE 1=1';
      const params: any[] = [];
      
      if (search) {
        whereClause += ' AND (u.email LIKE ? OR p.nama_lengkap LIKE ?)';
        params.push(`%${search}%`, `%${search}%`);
      }
      
      if (role) {
        whereClause += ' AND u.role = ?';
        params.push(role);
      }

      const countResult = await connection.getPool().execute(`
        SELECT COUNT(*) as count 
        FROM users u 
        LEFT JOIN profiles p ON u.id = p.user_id 
        ${whereClause}
      `, params);
      totalCount = (countResult as any)[0][0].count;

      const usersResult = await connection.getPool().execute(`
        SELECT u.id, u.email, u.role, u.is_active, u.email_verified, 
               u.last_login_at, u.created_at, p.nama_lengkap
        FROM users u
        LEFT JOIN profiles p ON u.id = p.user_id
        ${whereClause}
        ORDER BY u.created_at DESC
        LIMIT ? OFFSET ?
      `, [...params, Number(limit), offset]);
      users = (usersResult as any)[0];
    }

    // Convert SQLite integer booleans
    if (provider === 'sqlite') {
      users = users.map((user: any) => ({
        ...user,
        is_active: Boolean(user.is_active),
        email_verified: Boolean(user.email_verified)
      }));
    }

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total: totalCount,
          totalPages: Math.ceil(totalCount / Number(limit))
        }
      }
    });

  } catch (error) {
    console.error('Users list error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch users'
    });
  }
});

/**
 * User Management - Create User
 */
router.post('/users', requirePermission('admin.users.create'), async (req: AuthenticatedRequest, res) => {
  try {
    const { email, password, role, namaLengkap, isActive = true } = req.body;

    if (!email || !password || !namaLengkap) {
      return res.status(400).json({
        success: false,
        error: 'Email, password, and nama lengkap are required'
      });
    }

    const connectionManager = ConnectionManager.getInstance();
    const connection = await connectionManager.getConnection();
    const provider = connectionManager.getCurrentProvider();

    // Check if user exists
    let existingUser;
    if (provider === 'sqlite') {
      const stmt = connection.db.prepare('SELECT id FROM users WHERE email = ?');
      existingUser = stmt.get(email);
    } else {
      const result = await connection.getPool().execute('SELECT id FROM users WHERE email = ?', [email]);
      existingUser = (result as any)[0][0];
    }

    if (existingUser) {
      return res.status(409).json({
        success: false,
        error: 'User with this email already exists'
      });
    }

    const hashedPassword = await hashPassword(password);
    const userId = uuidv4();
    const profileId = uuidv4();
    const now = new Date().toISOString();

    if (provider === 'sqlite') {
      connection.db.transaction(() => {
        const userStmt = connection.db.prepare(`
          INSERT INTO users (id, email, password, role, is_active, email_verified, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, 0, ?, ?)
        `);
        userStmt.run(userId, email, hashedPassword, role, isActive ? 1 : 0, now, now);

        const profileStmt = connection.db.prepare(`
          INSERT INTO profiles (id, user_id, nama_lengkap, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?)
        `);
        profileStmt.run(profileId, userId, namaLengkap, now, now);
      })();
    } else {
      await connection.getPool().execute(`
        INSERT INTO users (id, email, password, role, is_active, email_verified, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, 0, NOW(), NOW())
      `, [userId, email, hashedPassword, role, isActive]);

      await connection.getPool().execute(`
        INSERT INTO profiles (id, user_id, nama_lengkap, created_at, updated_at)
        VALUES (?, ?, ?, NOW(), NOW())
      `, [profileId, userId, namaLengkap]);
    }

    // Log admin action
    await logAdminAction(req.user!.id, 'user.create', 'user', userId, null, {
      email, role, namaLengkap, isActive
    }, req.ip, req.get('User-Agent'));

    res.status(201).json({
      success: true,
      message: 'User created successfully',
      data: {
        id: userId,
        email,
        role,
        namaLengkap,
        isActive
      }
    });

  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create user'
    });
  }
});

/**
 * Log admin actions for audit trail
 */
async function logAdminAction(
  userId: string,
  action: string,
  resourceType: string,
  resourceId: string,
  oldValues: any,
  newValues: any,
  ipAddress?: string,
  userAgent?: string
) {
  try {
    const connectionManager = ConnectionManager.getInstance();
    const connection = await connectionManager.getConnection();
    const provider = connectionManager.getCurrentProvider();

    const logId = uuidv4();
    const now = new Date().toISOString();

    if (provider === 'sqlite') {
      const stmt = connection.db.prepare(`
        INSERT INTO audit_logs (id, user_id, action, resource_type, resource_id, old_values, new_values, ip_address, user_agent, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      stmt.run(
        logId, userId, action, resourceType, resourceId,
        JSON.stringify(oldValues), JSON.stringify(newValues),
        ipAddress, userAgent, now
      );
    } else {
      await connection.getPool().execute(`
        INSERT INTO audit_logs (id, user_id, action, resource_type, resource_id, old_values, new_values, ip_address, user_agent, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
      `, [
        logId, userId, action, resourceType, resourceId,
        JSON.stringify(oldValues), JSON.stringify(newValues),
        ipAddress, userAgent
      ]);
    }
  } catch (error) {
    console.error('Failed to log admin action:', error);
  }
}

export default router;
